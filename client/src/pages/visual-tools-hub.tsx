/**
 * Hub Central de Herramientas Visuales de Emma Studio
 * Diseño elegante inspirado en Freepik.com
 */
import React, { useState } from "react";
import { Link } from "wouter";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  ImageIcon,
  Video,
  Wand2,
  Scissors,
  ArrowUpRight,
  Layers,
  PencilRuler,
  ChevronRight,
  Gauge,
  Eraser,
  Image,
  FileImage,
  Sparkles,
  Boxes,
  ArrowLeft,
  ArrowRight,
  Search,
  Brush,
  Palette,
  Film,
  Zap,
  Settings,
  MoreHorizontal,
  PlaySquare,
  X,
  Star,
  TrendingUp,
  Heart,
  Hexagon,
  BarChart3,
  Smile,
  Megaphone,
} from "lucide-react";
import DashboardLayout from "@/components/layout/dashboard-layout";

// Importar imágenes personalizadas
import herramientaGenerador from "@/assets/herramienta-generador.png";
import herramientaMejora from "@/assets/herramienta-mejora.png";
import sketchAImagen from "@/assets/sketch-a-imagen.png";
import transferirEstilo from "@/assets/transferir-estilo-definitivo.png";
import herramientaReferencia from "@/assets/herramienta-referencia-transferencia.png";
import generacion3d from "@/assets/generacion-3d.png";
import avionVideo from "@/assets/avion-video.png";
import mujerReal from "@/assets/mujer-real.webp";
import editar from "@/assets/editar.png";
import borrador from "@/assets/borrador.png";
import eliminadorFondo from "@/assets/eliminador-fondo.png";
import sustituirFondo from "@/assets/sustituir-fondo.png";
import genLogosDefinitivo from "@/assets/gen-logos-definitivo.png";
import imagenInfografia from "@/assets/imagen-infogra.png";
import posterCreador from "@/assets/poster-creator.png";
import memeCreador from "@/assets/meme-creator.png";
import adCreador from "@/assets/ad-creator.png";
// Usar la imagen específica de infografía solicitada
const infografiaCreador = imagenInfografia;

const categories = [
  { id: "all", label: "Para ti", icon: <Sparkles className="w-5 h-5" /> },
  { id: "images", label: "Imágenes", icon: <ImageIcon className="w-5 h-5" /> },
  { id: "videos", label: "Videos", icon: <Video className="w-5 h-5" /> },
  {
    id: "vectors",
    label: "Vectores",
    icon: <PencilRuler className="w-5 h-5" />,
  },
  { id: "design", label: "Diseños", icon: <Palette className="w-5 h-5" /> },
  { id: "mockups", label: "Mockups", icon: <Layers className="w-5 h-5" /> },
  { id: "icons", label: "Iconos", icon: <Scissors className="w-5 h-5" /> },
  {
    id: "others",
    label: "Otros",
    icon: <MoreHorizontal className="w-5 h-5" />,
  },
];

// Definición del tipo para herramientas visuales
interface VisualTool {
  id: string;
  title: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  linkTo: string;
  isImplemented: boolean;
  isNew?: boolean;
  isPremium?: boolean;
  previewImage: string;
  gradient: string;
}

// Herramientas implementadas con diseño elegante estilo Freepik
const implementedTools: VisualTool[] = [
  {
    id: "ai-image-generator",
    title: "Generador de Imágenes IA",
    description: "Crea imágenes impresionantes desde texto con inteligencia artificial avanzada",
    category: "Generar",
    icon: <Wand2 className="w-6 h-6" />,
    linkTo: "/image-generator",
    isImplemented: true,
    previewImage: mujerReal,
    gradient: "from-violet-500 to-purple-600",
  },
  {
    id: "image-to-video",
    title: "Imagen a Video",
    description: "Convierte cualquier imagen en un video animado con movimiento realista",
    category: "Generar",
    icon: <PlaySquare className="w-6 h-6" />,
    linkTo: "/image-to-video",
    isImplemented: true,
    isNew: false,
    previewImage: avionVideo,
    gradient: "from-blue-500 to-indigo-600",
  },
  {
    id: "ai-image-editor",
    title: "Editor de Imágenes IA",
    description: "Edita y transforma imágenes con instrucciones de texto usando IA",
    category: "Editar",
    icon: <Brush className="w-6 h-6" />,
    linkTo: "/ai-image-editor",
    isImplemented: true,
    isNew: false,
    previewImage: editar,
    gradient: "from-emerald-500 to-green-600",
  },
  {
    id: "background-remover",
    title: "Eliminador de Fondos",
    description: "Elimina fondos de imágenes con precisión profesional en segundos",
    category: "Editar",
    icon: <Eraser className="w-6 h-6" />,
    linkTo: "/background-remover",
    isImplemented: true,
    previewImage: eliminadorFondo,
    gradient: "from-purple-500 to-violet-600",
  },
  {
    id: "sketch-to-image",
    title: "Sketch a Imagen",
    description: "Convierte bocetos y dibujos simples en imágenes detalladas",
    category: "Generar",
    icon: <PencilRuler className="w-6 h-6" />,
    linkTo: "/sketch-to-image",
    isImplemented: true,
    isNew: false,
    previewImage: sketchAImagen,
    gradient: "from-teal-500 to-cyan-600",
  },
  {
    id: "style-transfer",
    title: "Transferencia de Estilo",
    description: "Aplica el estilo artístico de una imagen a otra manteniendo el contenido",
    category: "Editar",
    icon: <Palette className="w-6 h-6" />,
    linkTo: "/style-transfer",
    isImplemented: true,
    isNew: false,
    previewImage: transferirEstilo,
    gradient: "from-rose-500 to-pink-600",
  },
  {
    id: "style-reference",
    title: "Estilo de Referencia",
    description: "Aplica el estilo de una imagen de referencia a nuevas generaciones con IA",
    category: "Generar",
    icon: <Palette className="w-6 h-6" />,
    linkTo: "/style-reference",
    isImplemented: true,
    isNew: false,
    previewImage: herramientaReferencia,
    gradient: "from-purple-500 to-indigo-600",
  },
  {
    id: "image-enhancer",
    title: "Mejorador de Imagen",
    description: "Mejora la resolución y calidad de tus imágenes con IA avanzada",
    category: "Mejorar",
    icon: <ArrowUpRight className="w-6 h-6" />,
    linkTo: "/image-enhancer",
    isImplemented: true,
    isPremium: false,
    previewImage: herramientaMejora,
    gradient: "from-amber-500 to-orange-600",
  },
  {
    id: "image-erase-tool",
    title: "Borrador Mágico",
    description: "Elimina objetos no deseados de tus imágenes con precisión",
    category: "Editar",
    icon: <Eraser className="w-6 h-6" />,
    linkTo: "/image-erase-tool",
    isImplemented: true,
    isNew: false,
    previewImage: borrador,
    gradient: "from-red-500 to-pink-600",
  },
  {
    id: "replace-background",
    title: "Reemplazar Fondo",
    description: "Cambia el fondo de tus imágenes y ajusta la iluminación automáticamente",
    category: "Editar",
    icon: <Image className="w-6 h-6" />,
    linkTo: "/replace-background",
    isImplemented: true,
    isNew: false,
    previewImage: sustituirFondo,
    gradient: "from-indigo-500 to-purple-600",
  },
  {
    id: "3d-generator",
    title: "Generación 3D",
    description: "Crea modelos 3D realistas a partir de imágenes con IA",
    category: "Generar",
    icon: <Boxes className="w-6 h-6" />,
    linkTo: "/3d-generator",
    isImplemented: true,
    isPremium: false,
    previewImage: generacion3d,
    gradient: "from-slate-500 to-gray-600",
  },
  {
    id: "logo-generator",
    title: "Generación de Logos",
    description: "Crea logos profesionales y modernos para tu marca con IA especializada",
    category: "Generar",
    icon: <Hexagon className="w-6 h-6" />,
    linkTo: "/logo-generator",
    isImplemented: true,
    isNew: false,
    previewImage: genLogosDefinitivo,
    gradient: "from-orange-500 to-red-600",
  },
  {
    id: "infographic-creator",
    title: "Crear Infografía",
    description: "Diseña infografías profesionales con IA. Generación, edición multi-turn y streaming en tiempo real",
    category: "Generar",
    icon: <BarChart3 className="w-6 h-6" />,
    linkTo: "/infographic-creator",
    isImplemented: true,
    isNew: false,
    previewImage: infografiaCreador,
    gradient: "from-blue-500 to-cyan-600",
  },
  {
    id: "poster-creator",
    title: "Crear Póster",
    description: "Diseña pósters profesionales con IA. Generación, edición multi-turn y streaming en tiempo real",
    category: "Generar",
    icon: <Palette className="w-6 h-6" />,
    linkTo: "/poster-creator",
    isImplemented: true,
    isNew: false,
    previewImage: posterCreador,
    gradient: "from-orange-500 to-red-600",
  },
  {
    id: "meme-creator",
    title: "Crear Meme",
    description: "Crea memes divertidos y virales con IA. Usa formatos populares, plantillas interactivas y referencias visuales",
    category: "Generar",
    icon: <Smile className="w-6 h-6" />,
    linkTo: "/meme-creator",
    isImplemented: true,
    isNew: false,
    previewImage: memeCreador,
    gradient: "from-purple-500 to-pink-600",
  },
  {
    id: "ad-creator",
    title: "Crear Anuncio",
    description: "Diseña anuncios profesionales con calidad de agencia. Product placement perfecto, iluminación profesional y composición comercial",
    category: "Generar",
    icon: <Megaphone className="w-6 h-6" />,
    linkTo: "/ad-creator",
    isImplemented: true,
    isNew: false,
    previewImage: adCreador,
    gradient: "from-emerald-500 to-teal-600",
  },
];

// Categorías de herramientas
const toolCategories = [
  { id: "all", name: "Todas", icon: <Sparkles className="w-5 h-5" /> },
  { id: "Generar", name: "Generar", icon: <Wand2 className="w-5 h-5" /> },
  { id: "Editar", name: "Editar", icon: <Brush className="w-5 h-5" /> },
  { id: "Mejorar", name: "Mejorar", icon: <ArrowUpRight className="w-5 h-5" /> },
];

export default function VisualToolsHub() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Filtrar herramientas según búsqueda y categoría
  const filteredTools = implementedTools.filter((tool) => {
    const matchesSearch =
      searchQuery === "" ||
      tool.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategory === "all" || tool.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Herramientas destacadas (las más populares)
  const featuredTools = implementedTools.slice(0, 3);

  return (
    <DashboardLayout pageTitle="Herramientas Visuales">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        {/* Hero Section - Mismo estilo que Vibe Marketing */}
        <div className="relative rounded-2xl overflow-hidden mb-8 backdrop-blur-xl">
          <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
          <div className="relative px-8 py-16 md:py-20 md:px-12">
            <div className="max-w-5xl">
              <div className="mb-8">
                <span className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30">
                  <Sparkles className="inline-block w-5 h-5 mr-2" />
                  Potenciado con IA
                </span>
                <h1 className="text-4xl md:text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
                  Hub{" "}
                  <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    Visual
                  </span>
                  <br className="hidden md:block" />
                  <span className="text-white/90 font-light">
                    herramientas de diseño IA
                  </span>
                </h1>
                <p className="text-white/90 text-xl md:text-2xl max-w-3xl font-light leading-relaxed mb-8">
                  Crea, edita y mejora contenido visual con inteligencia artificial avanzada.
                  Todo lo que necesitas para diseñar como un profesional.
                </p>

                {/* Barra de búsqueda mejorada */}
                <div className="relative max-w-lg mx-auto">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-white/30 to-white/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
                    <div className="relative bg-white/20 backdrop-blur-md rounded-full border border-white/30 shadow-2xl group-hover:bg-white/25 transition-all duration-300">
                      <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 text-white/80 w-5 h-5 group-hover:text-white transition-colors duration-300" />
                      <Input
                        type="text"
                        placeholder="Buscar herramientas de diseño..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-14 pr-6 py-4 w-full rounded-full border-0 bg-transparent text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 text-lg font-medium"
                      />
                      {searchQuery && (
                        <button
                          onClick={() => setSearchQuery("")}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors duration-200"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Indicador de búsqueda activa */}
                  {searchQuery && (
                    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                      Buscando: "{searchQuery}"
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Floating elements with glassmorphism */}
          <div className="absolute right-0 bottom-0 transform translate-y-1/4 -translate-x-10 hidden lg:block">
            <div className="relative w-72 h-72">
              <div className="absolute w-40 h-40 bg-white/20 backdrop-blur-md rounded-2xl -top-32 -left-20 transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30">
                <span className="text-7xl">🎨</span>
              </div>
              <div className="absolute w-44 h-44 bg-white/15 backdrop-blur-md rounded-2xl -top-10 left-20 transform -rotate-6 flex items-center justify-center shadow-2xl border border-white/20">
                <span className="text-7xl">✨</span>
              </div>
              <div className="absolute w-36 h-36 bg-white/25 backdrop-blur-md rounded-2xl top-24 -left-10 transform rotate-45 flex items-center justify-center shadow-2xl border border-white/40">
                <span className="text-6xl">🖼️</span>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-6 py-12">
          {/* Filtros de categoría */}
          <div className="flex flex-wrap gap-3 mb-12 justify-center">
            {toolCategories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className={`rounded-full px-6 py-2 transition-all duration-200 ${
                  selectedCategory === category.id
                    ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg"
                    : "hover:bg-gray-50 border-gray-200"
                }`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <span className="mr-2">{category.icon}</span>
                {category.name}
              </Button>
            ))}
          </div>

          {/* Herramientas destacadas */}
          {selectedCategory === "all" && !searchQuery && (
            <div className="mb-16">
              <div className="flex items-center gap-3 mb-8">
                <Star className="w-6 h-6 text-amber-500" />
                <h2 className="text-3xl font-bold text-gray-900">Herramientas Destacadas</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredTools.map((tool) => (
                  <Link key={tool.id} href={tool.linkTo} className="group">
                    <div className="relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2">
                      {/* Imagen de preview */}
                      <div className="relative h-48 overflow-hidden">
                        <img
                          src={tool.previewImage}
                          alt={tool.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />

                        {/* Badges */}
                        <div className="absolute top-4 left-4 flex gap-2">
                          {tool.isNew && (
                            <Badge className="bg-green-500 text-white border-0">
                              <TrendingUp className="w-3 h-3 mr-1" />
                              Nuevo
                            </Badge>
                          )}
                          {tool.isPremium && (
                            <Badge className="bg-amber-500 text-white border-0">
                              <Star className="w-3 h-3 mr-1" />
                              Premium
                            </Badge>
                          )}
                        </div>

                        {/* Icono de la herramienta */}
                        <div className="absolute bottom-4 left-4">
                          <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl text-white">
                            {tool.icon}
                          </div>
                        </div>
                      </div>

                      {/* Contenido */}
                      <div className="p-6">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">
                            {tool.title}
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {tool.category}
                          </Badge>
                        </div>
                        <p className="text-gray-600 text-sm leading-relaxed mb-4">
                          {tool.description}
                        </p>

                        <Button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white border-0">
                          Usar Herramienta
                          <ArrowUpRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Todas las herramientas */}
          <div className="mb-16">
            <div className="flex items-center gap-3 mb-8">
              <Sparkles className="w-6 h-6 text-indigo-500" />
              <h2 className="text-3xl font-bold text-gray-900">
                {searchQuery
                  ? `Resultados para "${searchQuery}"`
                  : selectedCategory === "all"
                    ? "Todas las Herramientas"
                    : `Herramientas de ${selectedCategory}`
                }
              </h2>
            </div>

            {filteredTools.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredTools.map((tool) => (
                  <Link key={tool.id} href={tool.linkTo} className="group">
                    <div className="relative overflow-hidden rounded-2xl bg-white shadow-md hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1">
                      {/* Imagen de preview */}
                      <div className="relative h-40 overflow-hidden">
                        <img
                          src={tool.previewImage}
                          alt={tool.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />

                        {/* Badges */}
                        <div className="absolute top-3 left-3 flex gap-2">
                          {tool.isNew && (
                            <Badge className="bg-green-500 text-white border-0 text-xs">
                              Nuevo
                            </Badge>
                          )}
                          {tool.isPremium && (
                            <Badge className="bg-amber-500 text-white border-0 text-xs">
                              Premium
                            </Badge>
                          )}
                        </div>

                        {/* Icono de la herramienta */}
                        <div className="absolute bottom-3 left-3">
                          <div className="p-2 bg-white/20 backdrop-blur-sm rounded-lg text-white">
                            {tool.icon}
                          </div>
                        </div>
                      </div>

                      {/* Contenido */}
                      <div className="p-5">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">
                            {tool.title}
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {tool.category}
                          </Badge>
                        </div>
                        <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2">
                          {tool.description}
                        </p>

                        <Button
                          size="sm"
                          className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white border-0"
                        >
                          Usar Ahora
                          <ArrowUpRight className="w-3 h-3 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-16 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl">
                <div className="max-w-md mx-auto">
                  <Search className="h-16 w-16 text-gray-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-3">
                    No se encontraron resultados
                  </h3>
                  <p className="text-gray-600 mb-8">
                    Intenta con otros términos o explora diferentes categorías
                  </p>
                  <Button
                    onClick={() => {
                      setSearchQuery("");
                      setSelectedCategory("all");
                    }}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white"
                  >
                    Ver todas las herramientas
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Footer con navegación */}
          <div className="mt-20 pt-12 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <Link href="/emma-studio">
                <Button variant="outline" className="flex items-center gap-2 hover:bg-gray-50">
                  <ArrowLeft className="h-4 w-4" />
                  Regresar a Emma Studio
                </Button>
              </Link>

              <div className="text-center">
                <p className="text-gray-600 text-sm">
                  ¿Necesitas ayuda? <Link href="/support" className="text-indigo-600 hover:text-indigo-700 font-medium">Contacta soporte</Link>
                </p>
              </div>

              <Link href="/video-tools">
                <Button variant="outline" className="flex items-center gap-2 hover:bg-gray-50">
                  Herramientas de Video
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
