/**
 * Página para reemplazar fondos y reajustar iluminación usando IA avanzada
 * Transformada para seguir el patrón de poster-creator, meme-creator y ad-creator
 */
import React, { useState, useCallback, useRef } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Paintbrush,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  Upload,
  Sparkles,
  ImageIcon,
  FileImage,
  Trash2,
  Settings,
  Zap,
  Sun,
  Layers,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ReplaceBackgroundOptions,
  replaceBackgroundWithPolling,
} from "@/services/stability-replace-background-service";

// Tipos para el estado de la aplicación
interface GeneratedBackground {
  id: string;
  image_url: string;
  prompt: string;
  metadata?: any;
  timestamp: number;
}

interface SavedBackground {
  id: string;
  image_url: string;
  prompt: string;
  metadata?: any;
  timestamp: number;
}

// Custom hook para localStorage
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Utilidades para fondos guardados
const SAVED_BACKGROUNDS_KEY = "emma-saved-backgrounds";

function createSavedBackground(data: {
  image_url: string;
  prompt: string;
  metadata?: any;
}): SavedBackground {
  return {
    id: `background-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    ...data,
  };
}

function isBackgroundSaved(imageUrl: string, savedBackgrounds: SavedBackground[]): boolean {
  return savedBackgrounds.some(bg => bg.image_url === imageUrl);
}

export default function ReplaceBackgroundPage() {
  const { toast } = useToast();

  // Estados principales
  const [currentBackground, setCurrentBackground] = useState<GeneratedBackground | null>(null);
  const [subjectImage, setSubjectImage] = useState<File | null>(null);
  const [subjectImagePreview, setSubjectImagePreview] = useState<string | null>(null);
  const [backgroundPrompt, setBackgroundPrompt] = useState<string>("cinematic lighting");
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  // Estados de configuración (sin seed visible)
  const [backgroundImage, setBackgroundImage] = useState<File | null>(null);
  const [backgroundImagePreview, setBackgroundImagePreview] = useState<string | null>(null);
  const [lightImage, setLightImage] = useState<File | null>(null);
  const [lightImagePreview, setLightImagePreview] = useState<string | null>(null);
  const [foregroundPrompt, setForegroundPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [preserveOriginalSubject, setPreserveOriginalSubject] = useState<number>(0.6);
  const [originalBackgroundDepth, setOriginalBackgroundDepth] = useState<number>(0.5);
  const [lightSourceDirection, setLightSourceDirection] = useState<string>("none");
  const [lightSourceStrength, setLightSourceStrength] = useState<number>(0.3);
  const [outputFormat, setOutputFormat] = useState<"webp" | "jpeg" | "png">("webp");

  // Estados para favoritos
  const [savedBackgrounds, setSavedBackgrounds] = useLocalStorage<SavedBackground[]>(SAVED_BACKGROUNDS_KEY, []);
  const [currentBackgroundSaved, setCurrentBackgroundSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const backgroundInputRef = useRef<HTMLInputElement>(null);
  const lightInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para reemplazo de fondo
  const examplePrompts = [
    "Fondo de estudio profesional con iluminación cinematográfica",
    "Paisaje natural con montañas y cielo azul",
    "Oficina moderna con ventanas grandes y luz natural",
    "Playa tropical con arena blanca y agua cristalina",
    "Ciudad futurista con rascacielos y luces neón",
    "Bosque encantado con rayos de sol filtrados"
  ];

  // Manejar la selección de imagen del sujeto
  const handleSubjectImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      // Crear URL para previsualización
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setSubjectImagePreview(result);
        setSubjectImage(file);
        setCurrentBackground(null); // Limpiar resultado anterior
      };
      reader.readAsDataURL(file);

      toast({
        title: "Imagen cargada",
        description: "Ahora puedes reemplazar el fondo y ajustar la iluminación.",
      });
    },
    [toast],
  );

  // Manejar la selección de imagen de fondo
  const handleBackgroundImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setBackgroundImagePreview(result);
        setBackgroundImage(file);
      };
      reader.readAsDataURL(file);

      toast({
        title: "Imagen de fondo cargada",
        description: "Esta imagen se usará como referencia para el fondo.",
      });
    },
    [toast],
  );

  // Manejar la selección de imagen de iluminación
  const handleLightImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLightImagePreview(result);
        setLightImage(file);
      };
      reader.readAsDataURL(file);

      toast({
        title: "Imagen de iluminación cargada",
        description: "Esta imagen se usará como referencia para la iluminación.",
      });
    },
    [toast],
  );

  // Función principal para reemplazar fondo
  const handleReplaceBackground = useCallback(async () => {
    if (!subjectImage) {
      toast({
        title: "Imagen requerida",
        description: "Por favor, selecciona una imagen del sujeto.",
        variant: "destructive",
      });
      return;
    }

    if (!backgroundPrompt && !backgroundImage) {
      toast({
        title: "Fondo requerido",
        description: "Proporciona un prompt de fondo o una imagen de referencia.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      // Generar seed aleatorio automáticamente (oculto del usuario)
      const randomSeed = Math.floor(Math.random() * 4294967294);

      const options: ReplaceBackgroundOptions = {
        subjectImage,
        backgroundPrompt: backgroundPrompt || undefined,
        backgroundReference: backgroundImage || undefined,
        foregroundPrompt: foregroundPrompt || undefined,
        negativePrompt: negativePrompt || undefined,
        preserveOriginalSubject,
        originalBackgroundDepth,
        lightSourceDirection: (lightSourceDirection !== "none" ? lightSourceDirection : undefined) as any,
        lightReference: lightImage || undefined,
        lightSourceStrength: lightSourceDirection !== "none" || lightImage ? lightSourceStrength : undefined,
        seed: randomSeed, // Seed aleatorio automático
        outputFormat,
      };

      const updateProgressCallback = (progressValue: number, message?: string) => {
        setProgress(progressValue);
      };

      const resultUrl = await replaceBackgroundWithPolling(
        options,
        updateProgressCallback,
        40,
        10000,
      );

      const newBackground: GeneratedBackground = {
        id: Date.now().toString(),
        image_url: resultUrl,
        prompt: backgroundPrompt || "Imagen de referencia",
        metadata: {
          preserveOriginalSubject,
          originalBackgroundDepth,
          lightSourceDirection,
          lightSourceStrength,
          outputFormat,
          seed: randomSeed,
        },
        timestamp: Date.now(),
      };

      setCurrentBackground(newBackground);

      toast({
        title: "¡Fondo reemplazado!",
        description: "El fondo ha sido reemplazado exitosamente.",
      });
    } catch (error) {
      console.error("Error al reemplazar fondo:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al reemplazar el fondo",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  }, [
    subjectImage,
    backgroundPrompt,
    backgroundImage,
    foregroundPrompt,
    negativePrompt,
    preserveOriginalSubject,
    originalBackgroundDepth,
    lightSourceDirection,
    lightImage,
    lightSourceStrength,
    outputFormat,
    toast,
  ]);

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!currentBackground) return;

    try {
      if (currentBackgroundSaved) {
        // Quitar de favoritos
        const savedBackground = savedBackgrounds.find(bg => bg.image_url === currentBackground.image_url);
        if (savedBackground) {
          const filteredBackgrounds = savedBackgrounds.filter(bg => bg.id !== savedBackground.id);
          setSavedBackgrounds(filteredBackgrounds);
          setCurrentBackgroundSaved(false);

          toast({
            title: "💔 Eliminado de favoritos",
            description: "El fondo ha sido eliminado de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const backgroundData = {
          image_url: currentBackground.image_url,
          prompt: currentBackground.prompt,
          metadata: currentBackground.metadata,
        };

        const newBackground = createSavedBackground(backgroundData);
        const updatedBackgrounds = [newBackground, ...savedBackgrounds].slice(0, 50); // Limitar a 50

        setSavedBackgrounds(updatedBackgrounds);
        setCurrentBackgroundSaved(true);

        toast({
          title: "❤️ ¡Guardado en favoritos!",
          description: "Fondo guardado exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo guardar el fondo. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [currentBackground, currentBackgroundSaved, savedBackgrounds, setSavedBackgrounds, toast]);

  // Función para descargar imagen
  const handleDownload = useCallback(async () => {
    if (!currentBackground?.image_url) return;

    try {
      const response = await fetch(currentBackground.image_url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `fondo-reemplazado-${Date.now()}.${outputFormat}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "📥 Descarga iniciada",
        description: "La imagen se está descargando.",
      });
    } catch (error) {
      toast({
        title: "❌ Error en descarga",
        description: "No se pudo descargar la imagen",
        variant: "destructive",
      });
    }
  }, [currentBackground, outputFormat, toast]);

  // Función para compartir
  const handleShare = useCallback(async () => {
    if (!currentBackground?.image_url) return;

    try {
      await navigator.clipboard.writeText(currentBackground.image_url);
      toast({
        title: "🔗 Enlace copiado",
        description: "El enlace de la imagen se copió al portapapeles",
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "No se pudo copiar el enlace",
        variant: "destructive",
      });
    }
  }, [currentBackground, toast]);

  // Verificar si el fondo actual está guardado
  React.useEffect(() => {
    if (currentBackground?.image_url) {
      setCurrentBackgroundSaved(isBackgroundSaved(currentBackground.image_url, savedBackgrounds));
    }
  }, [currentBackground, savedBackgrounds]);

  return (
    <DashboardLayout pageTitle="Reemplazar Fondo">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-purple-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-pink-600 to-red-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Layers className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Reemplazar Fondo
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Reemplaza fondos y ajusta la iluminación con IA avanzada. Crea composiciones profesionales en segundos.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Paintbrush className="w-3 h-3 mr-1" />
                  Reemplazo inteligente
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sun className="w-3 h-3 mr-1" />
                  Ajuste de iluminación
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Referencias visuales
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y reemplaza el fondo perfecto
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="upload" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 mb-6">
                    <TabsTrigger value="upload" className="text-xs">
                      <Upload className="h-3 w-3 mr-1" />
                      Subir Imagen
                    </TabsTrigger>
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Configurar
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Subir Imagen */}
                  <TabsContent value="upload" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imagen del sujeto</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/webp"
                          onChange={handleSubjectImageSelect}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Seleccionar Imagen
                        </Button>
                        <p className="text-xs text-gray-500">
                          JPEG, PNG, WebP hasta 10MB
                        </p>
                      </div>

                      {subjectImagePreview && (
                        <div className="relative">
                          <img
                            src={subjectImagePreview}
                            alt="Preview"
                            className="w-full h-48 object-cover rounded-lg border"
                          />
                          <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                            {subjectImage?.name}
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Tab: Configurar */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción del nuevo fondo</Label>
                      <Textarea
                        placeholder="Describe el fondo que quieres crear..."
                        value={backgroundPrompt}
                        onChange={(e) => setBackgroundPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    {/* Imagen de referencia para fondo */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imagen de referencia (opcional)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={backgroundInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleBackgroundImageSelect}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => backgroundInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Subir Referencia
                        </Button>
                        <p className="text-xs text-gray-500">
                          Para el estilo del fondo
                        </p>
                      </div>

                      {backgroundImagePreview && (
                        <div className="relative">
                          <img
                            src={backgroundImagePreview}
                            alt="Referencia de fondo"
                            className="w-full h-24 object-cover rounded-lg border"
                          />
                          <Button
                            variant="destructive"
                            size="sm"
                            className="absolute top-1 right-1 h-6 w-6 p-0"
                            onClick={() => {
                              setBackgroundImage(null);
                              setBackgroundImagePreview(null);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Configuración avanzada */}
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center">
                          <Label className="text-sm font-medium">Preservar sujeto original</Label>
                          <span className="text-sm text-muted-foreground">
                            {preserveOriginalSubject.toFixed(2)}
                          </span>
                        </div>
                        <Slider
                          value={[preserveOriginalSubject]}
                          onValueChange={(values) => setPreserveOriginalSubject(values[0])}
                          max={1}
                          min={0}
                          step={0.05}
                          className="w-full mt-2"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          1.0 = coincidencia exacta, 0.0 = nuevas cualidades de iluminación
                        </p>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Dirección de la luz</Label>
                        <Select value={lightSourceDirection} onValueChange={setLightSourceDirection}>
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">Sin dirección específica</SelectItem>
                            <SelectItem value="above">Desde arriba</SelectItem>
                            <SelectItem value="below">Desde abajo</SelectItem>
                            <SelectItem value="left">Desde la izquierda</SelectItem>
                            <SelectItem value="right">Desde la derecha</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Formato de salida</Label>
                        <Select value={outputFormat} onValueChange={(value: "webp" | "jpeg" | "png") => setOutputFormat(value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="webp">WebP (recomendado)</SelectItem>
                            <SelectItem value="jpeg">JPEG</SelectItem>
                            <SelectItem value="png">PNG</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <Button
                      onClick={handleReplaceBackground}
                      disabled={isGenerating || !subjectImage || (!backgroundPrompt && !backgroundImage)}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Procesando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Reemplazar Fondo
                        </>
                      )}
                    </Button>

                    {/* Progreso */}
                    {isGenerating && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progreso</span>
                          <span>{progress}%</span>
                        </div>
                        <Progress value={progress} className="w-full" />
                        <p className="text-sm text-muted-foreground">
                          Esto puede tardar varios minutos...
                        </p>
                      </div>
                    )}

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setBackgroundPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>

          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedBackgrounds.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
                {/* Fondo generado */}
                {currentBackground && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <Zap className="h-5 w-5 text-purple-600" />
                          Fondo Reemplazado
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleToggleFavorite}
                            className={currentBackgroundSaved ? "text-red-500 border-red-200 bg-red-50" : ""}
                          >
                            <Heart className={`h-4 w-4 mr-1 ${currentBackgroundSaved ? "fill-current" : ""}`} />
                            {currentBackgroundSaved ? "Guardado" : "Guardar"}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleShare}
                          >
                            <Share2 className="h-4 w-4 mr-1" />
                            Compartir
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleDownload}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Descargar
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="relative group">
                        <img
                          src={currentBackground.image_url}
                          alt="Fondo reemplazado"
                          className="w-full rounded-lg shadow-lg"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                      </div>

                      {/* Información del fondo */}
                      <div className="mt-4 space-y-2">
                        <div className="flex items-start gap-2">
                          <Badge variant="secondary" className="mt-0.5">Prompt</Badge>
                          <p className="text-sm text-gray-600 flex-1">{currentBackground.prompt}</p>
                        </div>

                        {currentBackground.metadata && (
                          <div className="flex items-center gap-2 text-xs text-gray-400">
                            <span>Preservación: {currentBackground.metadata.preserveOriginalSubject}</span>
                            <span>• Formato: {currentBackground.metadata.outputFormat}</span>
                            {currentBackground.metadata.lightSourceDirection !== "none" && (
                              <span>• Luz: {currentBackground.metadata.lightSourceDirection}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Estado vacío */}
                {!currentBackground && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center h-64 text-center">
                      <Layers className="h-16 w-16 text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold text-gray-600 mb-2">
                        No hay fondo reemplazado
                      </h3>
                      <p className="text-gray-500 mb-4">
                        Sube una imagen y configura el nuevo fondo para comenzar
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                {savedBackgrounds.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {savedBackgrounds.map((background) => (
                      <Card key={background.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-sm font-medium truncate">
                              Fondo #{background.id.slice(-6)}
                            </CardTitle>
                            <Badge variant="outline" className="text-xs">
                              {background.metadata?.outputFormat || 'webp'}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="relative group">
                            <img
                              src={background.image_url}
                              alt="Fondo guardado"
                              className="w-full h-32 object-cover rounded-lg"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                          </div>

                          <div className="text-xs text-gray-500 space-y-1">
                            <p className="truncate"><strong>Prompt:</strong> {background.prompt}</p>
                            <p><strong>Fecha:</strong> {new Date(background.timestamp).toLocaleDateString()}</p>
                          </div>

                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={async () => {
                                try {
                                  const response = await fetch(background.image_url);
                                  const blob = await response.blob();
                                  const url = window.URL.createObjectURL(blob);
                                  const a = document.createElement("a");
                                  a.href = url;
                                  a.download = `fondo-guardado-${Date.now()}.${background.metadata?.outputFormat || 'webp'}`;
                                  document.body.appendChild(a);
                                  a.click();
                                  document.body.removeChild(a);
                                  window.URL.revokeObjectURL(url);
                                } catch (error) {
                                  toast({
                                    title: "Error en descarga",
                                    description: "No se pudo descargar la imagen",
                                    variant: "destructive",
                                  });
                                }
                              }}
                              className="flex-1"
                            >
                              <Download className="h-3 w-3 mr-1" />
                              Descargar
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={async () => {
                                try {
                                  await navigator.clipboard.writeText(background.image_url);
                                  toast({
                                    title: "Enlace copiado",
                                    description: "El enlace se copió al portapapeles",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Error",
                                    description: "No se pudo copiar el enlace",
                                    variant: "destructive",
                                  });
                                }
                              }}
                              className="flex-1"
                            >
                              <Share2 className="h-3 w-3 mr-1" />
                              Compartir
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const filteredBackgrounds = savedBackgrounds.filter(bg => bg.id !== background.id);
                                setSavedBackgrounds(filteredBackgrounds);
                                toast({
                                  title: "Fondo eliminado",
                                  description: "El fondo ha sido eliminado de tus favoritos.",
                                });
                              }}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center h-64 text-center">
                      <Heart className="h-16 w-16 text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold text-gray-600 mb-2">
                        No tienes fondos guardados
                      </h3>
                      <p className="text-gray-500 mb-4">
                        Los fondos que marques como favoritos aparecerán aquí
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}
