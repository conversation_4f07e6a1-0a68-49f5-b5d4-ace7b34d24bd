import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Trophy,
  Star,
  CheckCircle,
  Sparkles,
  Target,
  Award
} from 'lucide-react';

interface Achievement {
  category: string;
  achievement: string;
  description: string;
  icon: string;
  impact: string;
}

interface AchievementsTabProps {
  achievements: Achievement[];
}

const AchievementsTab: React.FC<AchievementsTabProps> = ({ achievements }) => {
  // Debug logging
  console.log('🏆 AchievementsTab - Received achievements:', achievements);
  console.log('🏆 AchievementsTab - Achievements type:', typeof achievements);
  console.log('🏆 AchievementsTab - Achievements length:', achievements?.length);

  const getCategoryColor = (category: string) => {
    const colors = {
      'Seguridad': 'bg-green-100 text-green-800 border-green-200',
      'Meta Tags': 'bg-blue-100 text-blue-800 border-blue-200',
      'Estructura': 'bg-purple-100 text-purple-800 border-purple-200',
      'Accesibilidad': 'bg-orange-100 text-orange-800 border-orange-200',
      'Contenido': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      'Navegación': 'bg-cyan-100 text-cyan-800 border-cyan-200',
      'SEO Técnico': 'bg-gray-100 text-gray-800 border-gray-200',
      'Mobile': 'bg-pink-100 text-pink-800 border-pink-200',
      'Redes Sociales': 'bg-violet-100 text-violet-800 border-violet-200',
      'Rendimiento': 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      'Seguridad': '🔒',
      'Meta Tags': '📝',
      'Estructura': '🏗️',
      'Accesibilidad': '🖼️',
      'Contenido': '📚',
      'Navegación': '🔗',
      'SEO Técnico': '⚙️',
      'Mobile': '📱',
      'Redes Sociales': '📱',
      'Rendimiento': '⚡'
    };
    return icons[category as keyof typeof icons] || '✅';
  };

  if (!achievements || achievements.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            Analizando tus logros...
          </h3>
          <p className="text-gray-500">
            Emma está identificando todo lo que estás haciendo bien en tu sitio web.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Group achievements by category
  const groupedAchievements = achievements.reduce((acc, achievement) => {
    if (!acc[achievement.category]) {
      acc[achievement.category] = [];
    }
    acc[achievement.category].push(achievement);
    return acc;
  }, {} as Record<string, Achievement[]>);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border border-green-200">
        <div className="flex items-center justify-center gap-3 mb-3">
          <Trophy className="w-8 h-8 text-yellow-600" />
          <h2 className="text-2xl font-bold text-green-800">
            ¡Felicitaciones! 🎉
          </h2>
          <Trophy className="w-8 h-8 text-yellow-600" />
        </div>
        <p className="text-green-700 text-lg">
          Tu sitio web está haciendo <strong>{achievements.length} cosas excelentes</strong> para SEO
        </p>
        <div className="flex items-center justify-center gap-2 mt-3">
          <Sparkles className="w-5 h-5 text-yellow-500" />
          <span className="text-green-600 font-medium">
            Sigue así - estás en el camino correcto
          </span>
          <Sparkles className="w-5 h-5 text-yellow-500" />
        </div>
      </div>

      {/* Achievements by Category */}
      {Object.entries(groupedAchievements).map(([category, categoryAchievements], categoryIndex) => (
        <motion.div
          key={category}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: categoryIndex * 0.1 }}
        >
          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <span className="text-2xl">{getCategoryIcon(category)}</span>
                  </div>
                  <span className="text-xl text-green-800">{category}</span>
                </CardTitle>
                <Badge className={`${getCategoryColor(category)} border`}>
                  {categoryAchievements.length} logro{categoryAchievements.length > 1 ? 's' : ''}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {categoryAchievements.map((achievement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (categoryIndex * 0.1) + (index * 0.05) }}
                  className="bg-green-50 border border-green-200 rounded-lg p-4"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-bold text-green-800 text-lg mb-2 flex items-center gap-2">
                        <Star className="w-5 h-5 text-yellow-500" />
                        {achievement.achievement}
                      </h4>
                      
                      <p className="text-green-700 mb-3">
                        {achievement.description}
                      </p>
                      
                      <div className="bg-white border border-green-300 rounded-lg p-3">
                        <div className="flex items-start gap-2">
                          <Award className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium text-green-800">Impacto positivo:</span>
                            <p className="text-green-700 text-sm mt-1">{achievement.impact}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {/* Summary Footer */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6 text-center">
          <div className="flex items-center justify-center gap-3 mb-3">
            <Sparkles className="w-6 h-6 text-blue-600" />
            <h3 className="text-xl font-bold text-blue-800">
              ¡Excelente trabajo!
            </h3>
            <Sparkles className="w-6 h-6 text-blue-600" />
          </div>
          
          <p className="text-blue-700 mb-4">
            Tu sitio web tiene una base sólida de SEO. Ahora puedes enfocarte en las mejoras 
            específicas que Emma identificó en la pestaña de "Recomendaciones".
          </p>
          
          <div className="flex items-center justify-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-green-700 font-medium">
                {achievements.length} aspectos optimizados
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-blue-500" />
              <span className="text-blue-700 font-medium">
                Continúa mejorando con Emma
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AchievementsTab;
