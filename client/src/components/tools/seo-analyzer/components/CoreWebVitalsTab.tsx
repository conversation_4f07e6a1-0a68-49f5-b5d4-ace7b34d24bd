import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  G<PERSON>ge, 
  Zap, 
  Eye, 
  MousePointer, 
  BarChart3, 
  Clock,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface CoreWebVital {
  value: number | null;
  display_value: string;
  score: number;
  rating: 'good' | 'needs_improvement' | 'poor' | 'unknown';
}

interface CoreWebVitalsData {
  core_web_vitals: {
    largest_contentful_paint: CoreWebVital;
    first_input_delay: CoreWebVital;
    cumulative_layout_shift: CoreWebVital;
    first_contentful_paint: CoreWebVital;
    speed_index: CoreWebVital;
    time_to_interactive: CoreWebVital;
  };
  lighthouse_scores: {
    performance: number;
    accessibility: number;
    best_practices: number;
    seo: number;
  };
  overall_performance_score: number;
}

interface CoreWebVitalsTabProps {
  data: CoreWebVitalsData;
}

const CoreWebVitalsTab: React.FC<CoreWebVitalsTabProps> = ({ data }) => {
  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-600 bg-green-50 border-green-200';
      case 'needs_improvement': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRatingIcon = (rating: string) => {
    switch (rating) {
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'needs_improvement': return <AlertTriangle className="w-4 h-4" />;
      case 'poor': return <XCircle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const coreVitals = [
    {
      key: 'largest_contentful_paint',
      title: 'Largest Contentful Paint (LCP)',
      description: 'Tiempo hasta que aparece el contenido principal',
      icon: <Eye className="w-5 h-5" />,
      explanation: 'Mide cuánto tarda en aparecer el elemento más grande visible. Debe ser menor a 2.5 segundos.',
      goodThreshold: '< 2.5s',
      data: data.core_web_vitals.largest_contentful_paint
    },
    {
      key: 'first_input_delay',
      title: 'First Input Delay (FID)',
      description: 'Tiempo de respuesta a la primera interacción',
      icon: <MousePointer className="w-5 h-5" />,
      explanation: 'Mide cuánto tarda la página en responder cuando haces clic. Debe ser menor a 100ms.',
      goodThreshold: '< 100ms',
      data: data.core_web_vitals.first_input_delay
    },
    {
      key: 'cumulative_layout_shift',
      title: 'Cumulative Layout Shift (CLS)',
      description: 'Estabilidad visual de la página',
      icon: <BarChart3 className="w-5 h-5" />,
      explanation: 'Mide cuánto se mueven los elementos mientras carga la página. Debe ser menor a 0.1.',
      goodThreshold: '< 0.1',
      data: data.core_web_vitals.cumulative_layout_shift
    }
  ];

  const additionalMetrics = [
    {
      key: 'first_contentful_paint',
      title: 'First Contentful Paint',
      description: 'Primer contenido visible',
      icon: <Zap className="w-5 h-5" />,
      data: data.core_web_vitals.first_contentful_paint
    },
    {
      key: 'speed_index',
      title: 'Speed Index',
      description: 'Velocidad de carga visual',
      icon: <Gauge className="w-5 h-5" />,
      data: data.core_web_vitals.speed_index
    },
    {
      key: 'time_to_interactive',
      title: 'Time to Interactive',
      description: 'Tiempo hasta interactividad completa',
      icon: <Clock className="w-5 h-5" />,
      data: data.core_web_vitals.time_to_interactive
    }
  ];

  return (
    <div className="space-y-6">
      {/* Overall Performance Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="w-5 h-5" />
            Puntuación General de Rendimiento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke={data.overall_performance_score >= 90 ? "#10b981" :
                         data.overall_performance_score >= 50 ? "#f59e0b" : "#ef4444"}
                  strokeWidth="2"
                  strokeDasharray={`${data.overall_performance_score}, 100`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className={`text-2xl font-bold ${getScoreColor(data.overall_performance_score)}`}>
                  {Math.round(data.overall_performance_score)}
                </span>
              </div>
            </div>
          </div>

          {/* Explanation for low scores */}
          {data.overall_performance_score === 0 && (
            <div className="mt-4 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    ¡Tu sitio está TAN lento que Google ni lo puede medir! 😱
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      Cuando Google PageSpeed da puntuación 0, significa que tu sitio tiene problemas graves de velocidad.
                      No es un error de Emma, es que tu sitio necesita optimización urgente.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {data.overall_performance_score > 0 && data.overall_performance_score < 30 && (
            <div className="mt-4 p-4 bg-orange-50 border-l-4 border-orange-400 rounded-r-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-orange-800">
                    Velocidad muy lenta - Necesita optimización urgente
                  </h3>
                  <div className="mt-2 text-sm text-orange-700">
                    <p>
                      Tu sitio está muy lento según Google. Esto puede afectar tu posicionamiento y hacer que pierdas visitantes.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <p className="text-center text-sm text-gray-600 mt-4">
            Puntuación basada en métricas de Google Lighthouse
          </p>
        </CardContent>
      </Card>

      {/* Core Web Vitals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            Core Web Vitals
            <Badge variant="outline" className="ml-2">Factores de Ranking</Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Métricas oficiales de Google que afectan directamente tu posicionamiento
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {coreVitals.map((vital) => (
              <motion.div
                key={vital.key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-4 rounded-lg border ${getRatingColor(vital.data.rating)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    {vital.icon}
                    <div className="flex-1">
                      <h4 className="font-semibold">{vital.title}</h4>
                      <p className="text-sm opacity-80">{vital.description}</p>
                      <p className="text-xs mt-1 opacity-70">{vital.explanation}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-2 mb-1">
                      {getRatingIcon(vital.data.rating)}
                      <span className="font-mono text-lg">
                        {vital.data.display_value || 'N/A'}
                      </span>
                    </div>
                    {(vital.data.display_value === 'N/A' || !vital.data.display_value) && (
                      <div className="text-xs text-red-600 mb-1">
                        ⚠️ Muy lento para medir
                      </div>
                    )}
                    <Badge variant="outline" className="text-xs">
                      Objetivo: {vital.goodThreshold}
                    </Badge>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Lighthouse Scores */}
      <Card>
        <CardHeader>
          <CardTitle>Puntuaciones de Lighthouse</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(data.lighthouse_scores).map(([key, score]) => (
              <div key={key} className="text-center">
                <div className="mb-2">
                  <div className={`text-2xl font-bold ${getScoreColor(score)}`}>
                    {Math.round(score)}
                  </div>
                  <Progress value={score} className="h-2 mt-1" />
                </div>
                <p className="text-sm capitalize">
                  {key.replace('_', ' ')}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Additional Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Métricas Adicionales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {additionalMetrics.map((metric) => (
              <div key={metric.key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  {metric.icon}
                  <div>
                    <h5 className="font-medium">{metric.title}</h5>
                    <p className="text-sm text-gray-600">{metric.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="font-mono">
                    {metric.data.display_value || 'N/A'}
                  </span>
                  <div className={`text-xs ${getRatingColor(metric.data.rating)}`}>
                    {metric.data.rating.replace('_', ' ')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CoreWebVitalsTab;
